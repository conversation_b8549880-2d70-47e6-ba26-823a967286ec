[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "time-series-insight-assistant"
version = "0.1.0"
description = "一个智能的时间序列分析助手，提供自动模型识别、参数估计和可视化诊断功能"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "zym", email = "<EMAIL>"},
]
keywords = ["time-series", "arima", "forecasting", "statistics", "analysis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scipy>=1.7.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0",
    "statsmodels>=0.13.0",
    "click>=8.0.0",
    "rich>=10.0.0",
    "typer>=0.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
    "pre-commit>=2.15.0",
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0",
    "myst-parser>=0.15.0",
]

[project.urls]
Homepage = "https://github.com/zym9863/time-series-insight-assistant"
Repository = "https://github.com/zym9863/time-series-insight-assistant"
"Bug Tracker" = "https://github.com/zym9863/time-series-insight-assistant/issues"

[project.scripts]
tsia = "time_series_insight.cli.main:app"
time-series-insight = "time_series_insight.cli.main:app"

[tool.hatch.build.targets.wheel]
packages = ["time_series_insight"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["time_series_insight"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=time_series_insight",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "matplotlib.*",
    "seaborn.*",
    "statsmodels.*",
]
ignore_missing_imports = true
