"""
测试API功能的简单演示
"""

import pandas as pd
import numpy as np
from time_series_insight import analyze_time_series

# 创建简单的测试数据
np.random.seed(42)
dates = pd.date_range('2020-01-01', periods=100, freq='D')
values = np.cumsum(np.random.randn(100)) + 100
data = pd.Series(values, index=dates)

print("🔍 时间序列洞察助手 API 测试")
print("=" * 40)

# 一键分析
print("1. 执行一键分析...")
tsi = analyze_time_series(data, n_models=2)

# 获取摘要
print("\n2. 分析摘要:")
summary = tsi.get_summary()
print(f"   数据长度: {summary['data_info']['length']}")
print(f"   平稳性: {'是' if summary.get('stationarity', {}).get('is_stationary', False) else '否'}")

if 'best_model' in summary:
    print(f"   推荐模型: {summary['best_model']['type']}")
    print(f"   AIC: {summary['best_model']['aic']:.2f}")
    print(f"   适合度: {summary['best_model']['adequacy_score']:.0f}%")

# 尝试预测
print("\n3. 生成预测:")
try:
    forecast_result = tsi.predict(steps=5)
    forecast = forecast_result['forecast']
    print(f"   预测未来5期，预测值范围: {forecast.min():.2f} 到 {forecast.max():.2f}")
except Exception as e:
    print(f"   预测失败: {e}")

print("\n✅ API测试完成！")
